<?php
//create by zfy ExcellentSteelstock started 2018/06/11
class ExcellentSteelstockAction extends AbstractAction {
	
	public function __construct() {
		parent :: __construct();
	}

	//检查Session
	public function checkSession() {
		if( $_SESSION['dusername'] == '' || $_SESSION['duserid'] == '' ){
		  goURL( APP_URL_OA."/admincp/login.php" );
	    }
	}

    public function index($params){
		//hlf start 2019/9/24
			$data_arr=$this->getBdate();//通过设置的周期获取本期与上期时间,上月，去年同期
			$sdate1=$data_arr['sdata1'];
			$edate1=$data_arr['edata1'];
			$sdate2=$data_arr['sdata2'];
			$edate2=$data_arr['edata2'];
			$sdate3=$data_arr['sdata3'];
			$edate3=$data_arr['edata3'];
			$sdate4=$data_arr['sdata4'];
			$edate4=$data_arr['edata4'];
			$this->assign("sdate1",$sdate1);
			$this->assign("edate1",$edate1);
			$this->assign("sdate2",$sdate2);
			$this->assign("edate2",$edate2);
			$this->assign("sdate4",$sdate4);
			$this->assign("edate4",$edate4);
				
		
	//hlf end 2019/9/24
        //print_r($this->_dao);

        /*$year = date("Y",strtotime("Friday"));
        for($i=$year;$i>=2004;$i--){
            $array_year[] = $i; 
        }
        for($j=1;$j<=12;$j++){
            if($j<10){
                $array_month[] = "0".$j; 
            }else{
                $array_month[] = $j; 
            }
        }
        for($k=1;$k<=31;$k++){
            if($k<10){
                $array_day[] = "0".$k; 
            }else{
                $array_day[] = $k; 
            }
        }
        //本期
        $Friday = strtotime("Friday");
        $tyear = $year;
        $tmonth = date("m",$Friday);
        $tday = date("d",$Friday);
        $lyear = date("Y",strtotime("-6 day",$Friday));
        $lmonth = date("m",strtotime("-6 day",$Friday));
        $lday = date("d",strtotime("-6 day",$Friday));

        $this->assign("tyear",$tyear);
        $this->assign("tmonth",$tmonth);
        $this->assign("tday",$tday);
        $this->assign("lyear",$lyear);
        $this->assign("lmonth",$lmonth);
        $this->assign("lday",$lday);

        //上期
        $lastFriday = strtotime("last Friday");
        $tyear2 = date("Y",$lastFriday);
        $tmonth2 = date("m",$lastFriday);
        $tday2 = date("d",$lastFriday);
        $lyear2 = date("Y",strtotime("-6 day",$lastFriday));
        $lmonth2 = date("m",strtotime("-6 day",$lastFriday));
        $lday2 = date("d",strtotime("-6 day",$lastFriday));

        $this->assign("tyear2",$tyear2);
        $this->assign("tmonth2",$tmonth2);
        $this->assign("tday2",$tday2);
        $this->assign("lyear2",$lyear2);
        $this->assign("lmonth2",$lmonth2);
        $this->assign("lday2",$lday2);

		//去年同期
        $lastyear = strtotime("-1 year");
		$lastyearFriday = strtotime("Friday",$lastyear);
        $tyear3 = date("Y",$lastyearFriday);
        $tmonth3 = date("m",$lastyearFriday);
        $tday3 = date("d",$lastyearFriday);
        $lyear3 = date("Y",strtotime("-6 day",$lastyearFriday));
        $lmonth3 = date("m",strtotime("-6 day",$lastyearFriday));
        $lday3 = date("d",strtotime("-6 day",$lastyearFriday));

        $this->assign("tyear3",$tyear3);
        $this->assign("tmonth3",$tmonth3);
        $this->assign("tday3",$tday3);
        $this->assign("lyear3",$lyear3);
        $this->assign("lmonth3",$lmonth3);
        $this->assign("lday3",$lday3);

        $this->assign("array_year",$array_year);
        $this->assign("array_month",$array_month);
        $this->assign("array_day",$array_day);*/
    }

    public function steelreview($params){
        //echo "<pre>";print_r($params);

        //print_r($this->_dao);
		//variety  1=山东  2=江苏
        $variety = $params['variety'];
       /* $stime = $params['syear']."-".$params['smonth']."-".$params['sday'];
        $etime = $params['eyear']."-".$params['emonth']."-".$params['eday'];

        $stime2 = $params['syear2']."-".$params['smonth2']."-".$params['sday2'];
        $etime2 = $params['eyear2']."-".$params['emonth2']."-".$params['eday2'];

        $stime3 = $params['syear3']."-".$params['smonth3']."-".$params['sday3'];
        $etime3 = $params['eyear3']."-".$params['emonth3']."-".$params['eday3'];*/

        //hlf start 2019/10/12
		$stime = $params['sdate1'];
        $etime = $params['edate1'];

        $stime2 = $params['sdate2'];
        $etime2 = $params['edate2'];

        $stime3 = $params['sdate4'];
        $etime3 = $params['edate4'];

		//hlf end

        
        $this->assign("nsourse","钢之家");
        //$this->assign("ntype","1");//文档类型 1 表格
        $this->assign("nchannelid","12");//特钢频道
        $this->assign("ncolumnid","003,239,003,282");// 钢厂资源 钢厂库存
        
        
        $this->assign("mobanhzh","1,2");//窄模板，加画中画
        //$this->assign("isfree","2");//聚焦当天收费
        $this->assign("tel",$_SESSION['tel']);
        $nkeys[101] = "山东,库存";
        $nkeys[102] = "江苏,库存";
        $nkeys[103] = "全国,库存";
		$nkeys[104] = "全国,库存,无缝钢管";
		$nkeys[105] = "全国,库存,工业线材";
        $nkeys[1011] = "山东,库存";
        $nkeys[1021] = "江苏,库存";
        $nowYear = date("Y");
        $threeYearsAgo = date("Y",strtotime("-2 year"));
        $ImageTitle[101] = urlencode(base64_encode($threeYearsAgo."年至今山东地区优特钢钢厂厂内周库存变化情况"));
        $ImageTitle[102] = urlencode(base64_encode($threeYearsAgo."年至今江苏地区优特钢钢厂厂内周库存变化情况"));
        $ImageTitle[103] = urlencode(base64_encode($threeYearsAgo."年至今全国地区优特钢钢厂厂内周库存变化情况"));
		$ImageTitle[104] = urlencode(base64_encode($threeYearsAgo."年至今全国地区无缝钢管钢厂厂内周库存变化情况"));
		$ImageTitle[105] = urlencode(base64_encode($threeYearsAgo."年至今全国地区工业线材钢厂厂内周库存变化情况"));

        $commonUrl = APP_URL_DC . '/v1.5/dcsearch.php?action=CommonSearch&SignCS=' . DATACENTER_SIGNCS . '&GUID=' . DATACENTER_GUID . '&DateStart=&DateEnd=&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=8&isbg=0&isjz=0&isfg=0&color=0&mc_type=0&mode=1&isEnglish=0&ChartsType=2&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&needCompress=0&ChartExtLineStyle=&isstaticimg=1';

        $url[101] = $commonUrl.'&ImageTitle=' . $ImageTitle[$variety] . '&'.str_replace('"','%22','DTIDJson=[{"DTID":"8443","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"yb22qzi80rjWs6e/4rTm","unitconver":"0","unitstring":"zfK21g==","iscustomerdb":"0"}]&ChartExt={"isfestival": "0","Type":"1","DateStr":"'.$threeYearsAgo.','.$nowYear.'"}').'&callbackname=tu1';
        $url[102] = $commonUrl.'&ImageTitle=' . $ImageTitle[$variety] . '&'.str_replace('"','%22','DTIDJson=[{"DTID":"8442","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"va3L1Te80rjWs6e/4rTm","unitconver":"0","unitstring":"zfK21g==","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"}]&ChartExt={"isfestival": "0","Type":"1","DateStr":"'.$threeYearsAgo.','.$nowYear.'"}').'&callbackname=tu1';
        $url[103] = $commonUrl.'&ImageTitle=' . $ImageTitle[$variety] . '&'.str_replace('"','%22','DTIDJson=[{"DTID":"91754","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"08XM2LjWxvPStb/itOYo1twp19y/4rTm","unitconver":"0","unitstring":"zfK21g==","iscustomerdb":"0"}]&ChartExt={"isfestival": "0","Type":"1","DateStr":"'.$threeYearsAgo.','.$nowYear.'"}').'&callbackname=tu1';
        $url[104] = "";
        $url[105] = $commonUrl.'&ImageTitle=' . $ImageTitle[$variety] . '&'.str_replace('"','%22','DTIDJson=[{"DTID":"148502","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"5bel5Lia57q/5p2Q5LyB5Lia5bqT5a2YKOWRqCnmgLvlupPlrZg=","unitconver":"0","unitstring":"zfK21g==","iscustomerdb":"0"}]&ChartExt={"isfestival": "0","Type":"1","DateStr":"'.$threeYearsAgo.','.$nowYear.'"}').'&callbackname=tu1';

        if ($url[$variety]!="")$image_content = '<div id="tu1" style="text-align: center;"><iframe name="iframe" style="margin:0; padding:0; width:98%;height:400px; background-color:#FFF; visibility:inherit;" src="' . $url[$variety] . '" frameborder="0" scrolling="no"></iframe></div>';

        switch ($variety) {
            case '101'://山东优钢库存
            case '102'://江苏优钢库存
                $sc_id_arr = $GLOBALS['EXCELLENT_STEEL_STOCK_SCID'][$variety];
                $scids = implode(',', $sc_id_arr);
                $data = $this->getdata_jc($stime, $etime, $stime2, $etime2, $stime3, $etime3, $scids, $variety);
                break;
            case '1011':
            case '1021':
                $sc_id_arr = $GLOBALS['EXCELLENT_STEEL_STOCK_SCID'][substr($variety, 0, -1)];
                $data = $this->getYTGTableData($stime, $etime, $stime2, $etime2, $stime3, $etime3, $sc_id_arr, $variety);
                break;
            case '103'://全国优钢库存
                $type1 = "9";
                $dta_vartype = "7";
                $data = $this->getCountryData($stime, $etime, $stime2, $etime2, $stime3, $etime3, $type1, $dta_vartype);
                break;
            case '104'://无缝钢管
                $type1 = "12";
                $dta_vartype = "10";
                $data = $this->getdata_wfgg($stime, $etime, $stime2, $etime2, $stime3, $etime3, $type1, $dta_vartype);
                break;
            case '105'://全国工业线材库存
                $type1 = "10";
                $dta_vartype = "8";
                $data = $this->getCountryData($stime, $etime, $stime2, $etime2, $stime3, $etime3, $type1, $dta_vartype);
                break;
        }
       
        $this->assign("nkeys",$nkeys[$variety]);
        $this->assign("title",$variety !='104'?date("n月j日",strtotime($etime)).$data['title']:$data['title']);
        $this->assign("content1",$data['content1']);
        $this->assign("content2",$data['content2']);
        $this->assign("image_content",$image_content);
        $this->assign("arr",$stime."|A|".$etime."|A|".$stime2."|A|".$etime2."|A|".$variety);
    }


    function zhangdie($data){
        //$data = number_format($data,2);
        if($data>0){
            return "<font color=red>".$data."</font>";
        }elseif($data<0){
            return "<font color=green>".$data."</font>";
        }else{
            return "-";
        }
    }

    function numformat($data){
        if($data==''){
            return "-";
        }else{
            return (float)$data;
        }
    }
    function numformat2($data){
        
        return (float)$data;
       
    }

    function zhangdie2($data){
        //$data = number_format($data,2);
        if($data>0){
            return "增加".abs($data)."万吨";
        }elseif($data<0){
            return "下降".abs($data)."万吨";
        }else{
            return "持平";
        }
    }
    function zhangdie3($data){
        //$data = number_format($data,2);
        if($data>0){
            return "增加".abs($data)."万吨";
        }elseif($data<0){
            return "减少".abs($data)."万吨";
        }else{
            return "持平";
        }
    }
    function zhangdie_jiantou($data){
        //$data = number_format($data,2);
        if($data>0){
            return "<font color=red>↑".abs($data)."</font>";
        }elseif($data<0){
            return "<font color=green>↓".abs($data)."</font>";
        }else{
            return "0";
        }
    }
	//计算环比
	function huanbi($edata,$sdata){
		if(!(empty($sdata)||empty($edata)))
		{
			$data = ($edata-$sdata)/$sdata*100;// echo $edata;echo "<br/>";echo $sdata;echo "<br/>";echo "<br/>";
			if($data>0){
				return "上涨".round(abs($data),2)."%";
			}elseif($data<0){
				return "下降".round(abs($data),2)."%";
			}else{
				return "持平";
			}
		}
		else
		{
			return "持平";
		}
		
	}
	function huanbi2($edata,$sdata){
		$data = $sdata?($edata-$sdata)/$sdata*100:0;// echo $edata;echo "<br/>";echo $sdata;echo "<br/>";echo "<br/>";
        if($data>0){
            return "上升".round(abs($data),2)."%";
        }elseif($data<0){
            return "下降".round(abs($data),2)."%";
        }else{
            return "持平";
        }
	}

    /**
     * 全国分区域多品种库存 通用版
     * Created by zfy.
     * Date:2025/1/21 10:05
     * @param $stime
     * @param $etime
     * @param $stime2
     * @param $etime2
     * @param $stime3
     * @param $etime3
     * @param $str
     * @param $dta_vartype
     * @return string[]
     */
    private function getCountryData($stime,$etime,$stime2,$etime2,$stime3,$etime3,$str,$dta_vartype)
    {
        if ($str == "10" && $dta_vartype == "8"){
            $varietyName = "工业线材";
            $varietyType = "品种线";
            $titleVariety = "工业线材";
        }elseif ($str == "9" && $dta_vartype == "7"){
            $varietyName = "优特钢";
            $varietyType = "棒材";
            $titleVariety = "优钢";
        }
        $ndate = date("n月j日",strtotime($etime));
        $ndate2 = date("n月j日",strtotime($etime2));
        $ndate3 = date("Y年n月j日",strtotime($etime3));
        $ytg_startdata = $this->_dao->get_steelarea_pzsum2($str,$stime2,$etime2,$dta_vartype);

        $ytg_enddata = $this->_dao->get_steelarea_pzsum2($str,$stime,$etime,$dta_vartype);

        //去年同期
        $ytg_lastyeardata = $this->_dao->get_steelarea_pzsum2($str,$stime3,$etime3,$dta_vartype);
        $ytg_enddatanum=0;
        foreach($ytg_enddata as $k=>$v)
        {
            $ytg_enddatanum+=$v;
        }
        $ytg_startdatanum=0;
        foreach($ytg_startdata as $k=>$v)
        {
            $ytg_startdatanum+=$v;
        }
        $ytg_lastyeardatanum=0;
        foreach($ytg_lastyeardata as $k=>$v)
        {
            $ytg_lastyeardatanum+=$v;
        }

        //获取品种统计钢厂数量
        $count = $this->_dao->get_steel_sum($str,$stime,$etime,$dta_vartype);

        $content1 = "据钢之家网站对国内".$count."家主要".$varietyName."生产企业库存调查显示，截止（".$ndate."）本周四，全国".$varietyName."生产企业".$varietyType."总库存为".$ytg_enddatanum."万吨，较上周（".$ndate2."）".$this->zhangdie3($ytg_enddatanum-$ytg_startdatanum)."，周环比".$this->huanbi2($ytg_enddatanum,$ytg_startdatanum);
        if($ytg_lastyeardatanum!=''||$ytg_lastyeardatanum!='0'){
            $content1.="，较去年同期（".$ndate3."）".$this->zhangdie3($ytg_enddatanum-$ytg_lastyeardatanum)."，同比".$this->huanbi2($ytg_enddatanum,$ytg_lastyeardatanum)."。<br>";
        }else{
            $content1.="。<br>";
        }
        $areaTypeMatch = ["hd"=>"华东地区","zn"=>"中南地区","hb"=>"华北地区","db"=>"东北地区"];

        foreach ($areaTypeMatch as $area_code => $areaName) {
            $firstText = $area_code == "hd" ? "其中，" : "";

            $content1 .= $firstText.$areaName."生产企业样本库存在".$ytg_enddata[$area_code]."万吨，较上周（".$ndate2."）".$this->zhangdie3($ytg_enddata[$area_code]-$ytg_startdata[$area_code])."，周环比".$this->huanbi2($ytg_enddata[$area_code],$ytg_startdata[$area_code]);
            if($ytg_lastyeardata[$area_code]!=''&&$ytg_lastyeardata[$area_code]!='0'){
                $content1.="，较去年同期（".$ndate3."）".$this->zhangdie3($ytg_enddata[$area_code]-$ytg_lastyeardata[$area_code])."，同比".$this->huanbi2($ytg_enddata[$area_code],$ytg_lastyeardata[$area_code])."。";
            }else{
                $content1.="。";
            }
        }

        //西部
        $content1 .= "西部地区生产企业样本库存在".((float)$ytg_enddata['xn']+(float)$ytg_enddata['xb'])."万吨，较上周（".$ndate2."）".$this->zhangdie3(((float)$ytg_enddata['xn']+(float)$ytg_enddata['xb'])-((float)$ytg_startdata['xn']+(float)$ytg_startdata['xb']))."，周环比".$this->huanbi2(((float)$ytg_enddata['xn']+(float)$ytg_enddata['xb']),((float)$ytg_startdata['xn']+(float)$ytg_startdata['xb']));
        if(((float)$ytg_lastyeardata['xn']+(float)$ytg_lastyeardata['xb'])!=0){
            $content1.="，较去年同期（".$ndate3."）".$this->zhangdie3(($ytg_enddata['xn']+$ytg_enddata['xb'])-((float)$ytg_lastyeardata['xn']+(float)$ytg_lastyeardata['xb']))."，同比".$this->huanbi2(($ytg_enddata['xn']+$ytg_enddata['xb']),((float)$ytg_lastyeardata['xn']+(float)$ytg_lastyeardata['xb']))."。";
        }else{
            $content1.="。";
        }
        $sd_photo_link = "";
        $title ="全国".$titleVariety."钢厂库存调研";
        return array("content1"=>$content1,"content2"=>$sd_photo_link,"title"=>$title);
    }

    protected function getYTGTableData($stime,$etime,$stime2,$etime2,$stime3,$etime3,$sc_id_arr,$variety)
    {
        $scids = implode(',', $sc_id_arr);
        $area = array(
            "1011"=>"山东",
            "1021"=>"江苏"
        );
        $areaName = $area[$variety];
        $ndate = date("n月j日",strtotime($etime));
        $ndate2 = date("n月j日",strtotime($etime2));
        $ndate3 = date("Y年n月j日",strtotime($etime3));
        //钢厂id对应简称
        $steelComNameList = $this->_dao->getSteelComNameByScId($scids);
        //优特棒
        $str = "9";
        $dta_vartype = "7";
        //设备类型
        $device_type = "35";
        $ytg_startdata = $this->_dao->get_stock_info_by_sc_id($str,$stime2,$etime2,$dta_vartype,$scids);
        $ytg_enddata = $this->_dao->get_stock_info_by_sc_id($str,$stime,$etime,$dta_vartype,$scids);
        //设备状态
        $deviceInfo = $this->_dao->get_device_info_by_sc_id($sc_id_arr,$device_type);
        $content1 = "<table  border='1' cellspacing='0' cellpadding='0' width='733' text-align='center' style='text-align:center;vertical-align:middle;font-size:14px; font-family:Times New Roman;border-collapse:collapse;'>
<tr style='line-height: 50px'><td colspan='6'><b>".$ndate.$areaName."优钢钢厂生产状况调查</b></td></tr>
<tr>
<td>钢厂</td>
<td>本期<br>库存<br>（万吨）</td>
<td>环比<br>增减<br>（万吨）</td>
<td>环比<br>增减<br>幅度</td>";
$content1 .= "<td>45#碳结圆钢<br>直发报价<br>（元/吨）</td>
<td>周价格变动<br>（元/吨）</td>";
$content1 .= "<td>生产状况</td>
</tr>";
        $startSum = 0;
        $endSum = 0;
        foreach ($sc_id_arr as $index => $sc_id) {
            $content1.="
            <tr style='line-height: 40px'>
                <td>".$steelComNameList[$sc_id]."</td>
                <td>".$ytg_enddata[$sc_id]."</td>
                <td>".$this->zhangdie_jiantou($ytg_enddata[$sc_id]-$ytg_startdata[$sc_id])."</td>
                <td></td>
                <td></td>
                <td style='text-align: left'>".$deviceInfo[$sc_id]."</td>
            </tr>";
            $startSum += $ytg_startdata[$sc_id];
            $endSum += $ytg_enddata[$sc_id];
        }
        $content1.="<tr style='line-height: 40px'><td>合计</td><td>".$endSum."</td><td>".$this->zhangdie_jiantou($endSum-$startSum)."</td><td></td><td></td><td></td></tr></table>";
        $sd_photo_link = "";
        $title =$area[$variety]."优钢钢厂生产状况调查";
        return array("content1"=>$content1,"content2"=>$sd_photo_link,"title"=>$title);
    }

    function getdata_jc($stime,$etime,$stime2,$etime2,$stime3,$etime3,$scids,$variety){
		$area = array(
			"101"=>"山东",
			"102"=>"江苏",
			"103"=>"全国",
		);
        $ndate = date("n月j日",strtotime($etime));
        $ndate2 = date("n月j日",strtotime($etime2));
        $ndate3 = date("Y年n月j日",strtotime($etime3));
        //优特棒
        $str = "9";
		$dta_vartype = "7";
        $ytg_startdata = $this->_dao->get_steelarea_pzsum($str,$stime2,$etime2,$dta_vartype,$scids);
		
        $ytg_enddata = $this->_dao->get_steelarea_pzsum($str,$stime,$etime,$dta_vartype,$scids);
		
		//去年同期
        $ytg_lastyeardata = $this->_dao->get_steelarea_pzsum($str,$stime3,$etime3,$dta_vartype,$scids);


        //获取品种统计钢厂数量
        $count = $this->_dao->get_steel_sum($str,$stime,$etime,$dta_vartype,$scids);

        $content1 = "钢之家网站初步统计，截止" . $ndate . $area[$variety] . $count . "家主要优钢企业圆钢库存在" . $ytg_enddata . "万吨，较上一期（" . $ndate2 . "）" . $this->zhangdie3($ytg_enddata - $ytg_startdata) . "，周环比" . $this->huanbi($ytg_enddata, $ytg_startdata);
        if ($ytg_lastyeardata != '' || $ytg_lastyeardata != '0') {
            $content1 .= "，较去年同期（" . $ndate3 . "）" . $this->zhangdie3($ytg_enddata - $ytg_lastyeardata) . "，同比" . $this->huanbi($ytg_enddata, $ytg_lastyeardata) . "。";
        } else {
            $content1 .= "。";
        }

//		$stocks = $this->_dao->get_stocks($stime3,$etime);
//		$sd_photo_link = $this->create_pic($stocks,$variety,$variety,$stime3,$etime);
        $sd_photo_link = "";
		$title =$area[$variety]."优钢钢厂库存调研";
        return array("content1"=>$content1,"content2"=>$sd_photo_link,"title"=>$title);
    }


	function getdata_wfgg($stime,$etime,$stime2,$etime2,$stime3,$etime3,$str,$dta_vartype){
		
        $ndate = date("n月j日",strtotime($etime));
        $ndate2 = date("n月j日",strtotime($etime2));
        $ndate3 = date("Y年n月j日",strtotime($etime3));
        $varietyName = "";
        if ($str == "12" && $dta_vartype == "10") {
            $varietyName = "无缝钢管";
        }elseif ($str == "10" && $dta_vartype == "8") {
            $varietyName = "工业线材";
        }
        $ytg_startdata = $this->_dao->get_steelarea_pzsum2($str,$stime2,$etime2,$dta_vartype);
		
        $ytg_enddata = $this->_dao->get_steelarea_pzsum2($str,$stime,$etime,$dta_vartype);
		
		//去年同期
        $ytg_lastyeardata = $this->_dao->get_steelarea_pzsum2($str,$stime3,$etime3,$dta_vartype);
		$ytg_enddatanum=0;
		foreach($ytg_enddata as $k=>$v)
		{
			$ytg_enddatanum+=$v;
		}
		$ytg_startdatanum=0;
		foreach($ytg_startdata as $k=>$v)
		{
			$ytg_startdatanum+=$v;
		}
		$ytg_lastyeardatanum=0;
		foreach($ytg_lastyeardata as $k=>$v)
		{
			$ytg_lastyeardatanum+=$v;
		}

        //获取品种统计钢厂数量
        $count = $this->_dao->get_steel_sum($str,$stime,$etime,$dta_vartype);
		
			$content1 = "据钢之家网站对国内".$count."家主要".$varietyName."生产企业库存调查显示，截止（".$ndate."）本周四，全国".$varietyName."生产企业".$varietyName."总库存为".$ytg_enddatanum."万吨，较上周（".$ndate2."）".$this->zhangdie3($ytg_enddatanum-$ytg_startdatanum)."，周环比".$this->huanbi2($ytg_enddatanum,$ytg_startdatanum);
			if($ytg_lastyeardatanum!=''&&$ytg_lastyeardatanum!='0'){
				$content1.="，较去年同期（".$ndate3."）".$this->zhangdie3($ytg_enddatanum-$ytg_lastyeardatanum)."，同比".$this->huanbi2($ytg_enddatanum,$ytg_lastyeardatanum)."。<br>";
			}else{
				$content1.="。<br>";
			}
			//华东
			$content1 =$content1. "其中，华东地区生产企业样本库存在".$ytg_enddata['hd']."万吨，较上周（".$ndate2."）".$this->zhangdie3($ytg_enddata['hd']-$ytg_startdata['hd'])."，周环比".$this->huanbi2($ytg_enddata['hd'],$ytg_startdata['hd']);
			if($ytg_lastyeardata['hd']!=''&&$ytg_lastyeardata['hd']!='0'){
				$content1.="，较去年同期（".$ndate3."）".$this->zhangdie3($ytg_enddata['hd']-$ytg_lastyeardata['hd'])."，同比".$this->huanbi2($ytg_enddata['hd'],$ytg_lastyeardata['hd'])."。";
			}else{
				$content1.="。";
			}
			//中南
			$content1 =$content1. "中南地区生产企业样本库存在".$ytg_enddata['zn']."万吨，较上周（".$ndate2."）".$this->zhangdie3($ytg_enddata['zn']-$ytg_startdata['zn'])."，周环比".$this->huanbi2($ytg_enddata['zn'],$ytg_startdata['zn']);
			if($ytg_lastyeardata['zn']!=''&&$ytg_lastyeardata['zn']!='0'){
				$content1.="，较去年同期（".$ndate3."）".$this->zhangdie3($ytg_enddata['zn']-$ytg_lastyeardata['zn'])."，同比".$this->huanbi2($ytg_enddata['zn'],$ytg_lastyeardata['zn'])."。";
			}else{
				$content1.="。";
			}
			//华北
			$content1 =$content1. "华北地区生产企业样本库存在".$ytg_enddata['hb']."万吨，较上周（".$ndate2."）".$this->zhangdie3($ytg_enddata['hb']-$ytg_startdata['hb'])."，周环比".$this->huanbi2($ytg_enddata['hb'],$ytg_startdata['hb']);
			if($ytg_lastyeardata['hb']!=''&&$ytg_lastyeardata['hb']!='0'){
				$content1.="，较去年同期（".$ndate3."）".$this->zhangdie3($ytg_enddata['hb']-$ytg_lastyeardata['hb'])."，同比".$this->huanbi2($ytg_enddata['hb'],$ytg_lastyeardata['hb'])."。";
			}else{
				$content1.="。";
			}
			//东北
			$content1 =$content1. "东北地区生产企业样本库存在".$ytg_enddata['db']."万吨，较上周（".$ndate2."）".$this->zhangdie3($ytg_enddata['db']-$ytg_startdata['db'])."，周环比".$this->huanbi2($ytg_enddata['db'],$ytg_startdata['db']);
			if($ytg_lastyeardata['db']!=''&&$ytg_lastyeardata['db']!='0'){
				$content1.="，较去年同期（".$ndate3."）".$this->zhangdie3($ytg_enddata['db']-$ytg_lastyeardata['db'])."，同比".$this->huanbi2($ytg_enddata['db'],$ytg_lastyeardata['db'])."。";
			}else{
				$content1.="。";
			}
			
			//西部
			$content1 =$content1. "西部地区生产企业样本库存在".((float)$ytg_enddata['xn']+(float)$ytg_enddata['xb'])."万吨，较上周（".$ndate2."）".$this->zhangdie3(((float)$ytg_enddata['xn']+(float)$ytg_enddata['xb'])-((float)$ytg_startdata['xn']+(float)$ytg_startdata['xb']))."，周环比".$this->huanbi2(((float)$ytg_enddata['xn']+(float)$ytg_enddata['xb']),((float)$ytg_startdata['xn']+(float)$ytg_startdata['xb']));
			if(((float)$ytg_lastyeardata['xn']+(float)$ytg_lastyeardata['xb'])!=0){
				$content1.="，较去年同期（".$ndate3."）".$this->zhangdie3(($ytg_enddata['xn']+$ytg_enddata['xb'])-((float)$ytg_lastyeardata['xn']+(float)$ytg_lastyeardata['xb']))."，同比".$this->huanbi2(($ytg_enddata['xn']+$ytg_enddata['xb']),((float)$ytg_lastyeardata['xn']+(float)$ytg_lastyeardata['xb']))."。";
			}else{
				$content1.="。";
			}
			
		
        $sd_photo_link = "";
		$title ="【钢厂库存】本周国内".$varietyName."钢厂库存（".date('n月j日',strtotime($stime))."-".date('n月j日',strtotime($etime))."）";
		$title ="【钢厂库存】本周国内".$varietyName."钢厂库存";
        return array("content1"=>$content1,"content2"=>$sd_photo_link,"title"=>$title);
    }

	public function create_pic($kc,$type,$picname,$sdate,$edate){
		foreach($kc as $kkey=>$kvalue){
			if($kvalue['type']==$type){
				$data_arr[$kvalue['type']]['time'][] = date("Y-m-d",strtotime($kvalue['time']));
				$data_arr[$kvalue['type']]['value'][] = $kvalue['value'];
			}		
		}
		//echo "<pre>";print_R($kc);
		$creat_img['price']['0'] = $data_arr[$type]['time'];
		$creat_img['price']['1'] = $data_arr[$type]['value'];
		$creat_img['table_type'] = "BarPlot";
		$creat_img['x_begin'] = "0";
		mkdir("/usr/local/www/www.steelhome.cn/uploadfile/".date("Y",strtotime($edate))."/".date("n",strtotime($edate)));
		$creat_img['imgname'] = "/usr/local/www/www.steelhome.cn/uploadfile/".date("Y",strtotime($edate))."/".date("n",strtotime($edate))."/".$edate."_".$picname.".png";
		//$creat_img['title'] = date("Y",strtotime($sdate))."年至今".$GLOBALS['AREA_TYPE'][$type]."地区优特钢钢厂厂内周库存变化情况";
		if($type=='103'){
			$creat_img['title'] = date("Y",strtotime($sdate))."年至今".$GLOBALS['AREA_TYPE'][$type]."地区优特钢钢厂厂内周库存变化情况";
		}else{
			$creat_img['title'] = date("Y",strtotime($sdate))."年至今".$GLOBALS['AREA_TYPE'][$type]."地区优特钢钢厂厂内周库存变化情况";
		}
		
		//$creat_img['lendtitle']['0'] = "山东优钢";
		//echo "<pre>";print_R($creat_img);exit;
		$data = http_build_query($creat_img);  //把参数转换成URL数据  
			
		$aContext = array('http' => array('method' => 'POST',  
							'header'  => 'Content-type: application/x-www-form-urlencoded',  
							'content' => $data ));  
			
		$cxContext  = stream_context_create($aContext); 
		unlink($creat_img['imgname']);
		file_get_contents($GLOBALS['sUrl'],false,$cxContext);
		$link = "　　<center><img src='".APP_URL_WWW."/uploadfile/".date("Y",strtotime($edate))."/".date("n",strtotime($edate))."/".$edate."_".$picname.".png'></center>";
		return $link;
	}

    public function choose_date($params){
		//获取1 个月的汇总日期
		if(empty($params['HzDate'])){
			$endDate=date("Y-m-d");
		}else{
			$endDate=$params['HzDate'];
		}
		 //获取当期的汇总日期
		$tmp_arr=$this->getBdate($endDate);
        //$settingDate = $this->_dao->getKC_EveryWeekSettingForNow($endDate);
		//$edata1=$settingDate['EndDate'];
		$edata1=$tmp_arr['edata1'];
		//本期，上期
		$StartDate=date("Y-m-d",strtotime($edata1."-2 month"));//读取两个月的汇总日期，算出1个月的本期与上期
		$date_arr1=$this->_dao->getKC_EveryWeekSettingForTwoMonth($StartDate,$edata1);
		$beginDate1=date("Y-m-d",strtotime($edata1."-1 month"));
		//上月同期
		//$last_m_endDate=date("Y-m-d",strtotime($edata1."-1 month"));
		$last_m_endDate=$tmp_arr['edata3'];
		$last_m_StartDate=date("Y-m-d",strtotime($last_m_endDate."-2 month"));//读取两个月的汇总日期，算出1个月的本期与上期
		$date_arr2=$this->_dao->getKC_EveryWeekSettingForTwoMonth($last_m_StartDate,$last_m_endDate);
		//print_r($date_arr2);
		$beginDate2=date("Y-m-d",strtotime($last_m_endDate."-1 month"));
	//	print_r($date_arr2);
		//去年同期
	//	$last_y_endDate=date("Y-m-d",strtotime($edata1."-1 year"));
		$last_y_endDate=$tmp_arr['edata4'];
		$last_y_StartDate=date("Y-m-d",strtotime($last_y_endDate."-2 month"));//读取两个月的汇总日期，算出1个月的本期与上期
		$date_arr3=$this->_dao->getKC_EveryWeekSettingForTwoMonth($last_y_StartDate,$last_y_endDate);
		$beginDate3=date("Y-m-d",strtotime($last_y_endDate."-1 month"));

		$i=0;
		foreach($date_arr1 as $key=>$value){
			if(strtotime($value['EndDate'])>=strtotime($beginDate1)){
				$list_date[$i]['edata1']=$value['EndDate'];
				$list_date[$i]['sdata1']=date("Y-m-d",strtotime($date_arr1[$key+1]['EndDate']."+ 1 day"));
				$list_date[$i]['edata2']=$date_arr1[$key+1]['EndDate'];
				$list_date[$i]['sdata2']=date("Y-m-d",strtotime($date_arr1[$key+2]['EndDate']."+ 1 day"));
				//上月同期
				$tmp_m_date_arr=array();
			//	$min_m[]=strtotime($date_arr2[0]['EndDate'])-strtotime($date_arr2[1]['EndDate']);
				foreach($date_arr2 as $lastmkey=>$last_m_value){
					//	print_r($lastmkey);
					$tmp_date=date("Y-m-d",strtotime($value['EndDate']."-1 month"));
					$m_cz=strtotime($tmp_date)-strtotime($last_m_value['EndDate']);
					$tmp_m_date_arr[$lastmkey]=abs($m_cz)/86400;
				}
				$min_date_m=min($tmp_m_date_arr);
				$tmp_arr_min=array();
				$n=0;
				foreach($tmp_m_date_arr as $m_min_k=>$m_min_v){
					if($m_min_v==$min_date_m){
						$tmp_arr_min[$n]['v']=$m_min_v;
						$tmp_arr_min[$n]['k']=$m_min_k;
						$n++;
					}
				}
				if(count($tmp_arr_min)>1){
					$str=$this->get_week($value['EndDate']);
					for($j=0;$j<count($tmp_arr_min);$j++){
						$str_1=$this->get_week($date_arr2[$tmp_arr_min[$j]['k']]['EndDate']);
						if($str==$str_1){
							$min_k=$tmp_arr_min[$j]['k'];
						}
					}
					if($min_k==""){
						$min_k=$tmp_arr_min[0]['k'];
					}
				}else{
					$min_k=$tmp_arr_min[0]['k'];
				}
			//	print_r($tmp_arr_min);
					//print_r($min_k);
				$list_date[$i]['edata3']=$date_arr2[$min_k]['EndDate'];
				$list_date[$i]['sdata3']=date("Y-m-d",strtotime($date_arr2[$min_k+1]['EndDate']."+ 1 day"));
				
				//print_r($list_date);exit;
				//去年同期
				$tmp_y_date_arr=array();
				foreach($date_arr3 as $lastykey=>$last_y_value){
					$tmp_date1=date("Y-m-d",strtotime($value['EndDate']."-1 year"));
					$y_cz=strtotime($tmp_date1)-strtotime($last_y_value['EndDate']);
					$tmp_y_date_arr[$lastykey]=abs($y_cz)/86400;
				}
				$min_date_y=min($tmp_y_date_arr);
				$tmp_arr_min=array();
				$y=0;
				foreach($tmp_y_date_arr as $y_min_k=>$y_min_v){
					if($y_min_v==$min_date_y){
						$tmp_arr_min[$y]['v']=$y_min_v;
						$tmp_arr_min[$y]['k']=$y_min_k;
						$y++;
					}
				}
				if(count($tmp_arr_min)>1){
					$str=$this->get_week($value['EndDate']);
					for($z=0;$z<count($tmp_arr_min);$z++){
						$str_1=$this->get_week($date_arr3[$tmp_arr_min[$z]['k']]['EndDate']);
						if($str==$str_1){
							$min_k=$tmp_arr_min[$z]['k'];
						}
					}
					if($min_k==""){
						$min_k=$tmp_arr_min[0]['k'];
					}
				}else{
					$min_k=$tmp_arr_min[0]['k'];
				}
				//print_r($tmp_arr_min);
				$list_date[$i]['edata4']=$date_arr3[$min_k]['EndDate'];
				$list_date[$i]['sdata4']=date("Y-m-d",strtotime($date_arr3[$min_k+1]['EndDate']."+ 1 day"));
				$i++;
			}
		}
	//	echo "<pre>";
		//print_r($list_date);exit;
		$this->assign("list_date",$list_date);
		$this->assign("HzDate",$HzDate);
	}

	function get_week($date){
    #  本月第一天
    $oneDay = date('Y-m-01', strtotime($date));
    #  本月天数
    $tolDay = date('d', strtotime("$oneDay +1 month -1 day"));
    #  获取今天的日期
    $day = date('d',strtotime($date));   
   //$day ='14';

    #  计算本月第一天是周几
    $week = date('w',strtotime($oneDay));
    #  获取本月第一周有多少天
    switch($week){
        case 0;
            $weekDay = 1;
            break;
        case 1;
            $weekDay = 7;
            break;
        case 2;
            $weekDay = 6;
            break;
        case 3;
            $weekDay = 5;
            break;
        case 4;
            $weekDay = 4;
            break;
        case 5;
            $weekDay = 3;
            break;
        case 6;
            $weekDay = 2;
            break;
    }
    #  本月除去第一周剩余的天数
    $days = $tolDay - $weekDay;
    #  本月除了第一周还剩余多少周
    $d = ceil($days/7);
    #  本月第二周的第一天
    $w = $weekDay +1;
 
 
 
    $i = '0';
    $d = '2';
    for($w;$w<=$tolDay;$w++){
        $i++;
        if($i == '8'){
            $i = '1';
            $d++;
        }
        if($day == $w){
            return $d;
        }
    }
}

	public function getBdate($date=""){
		if($date==""){
			$NowDate = date("Y-m-d");
		}else{
			$NowDate =$date;
		}
		$sdata1="";
		$edata1="";
		$sdata2="";
		$edata2="";
		$sdata3="";
		$edata3="";
		$sdata4="";
		$edata4="";
		
        //获取当期的汇总日期
        $settingDate = $this->_dao->getKC_EveryWeekSettingForNow($NowDate);
		$edata1=$settingDate['EndDate'];
		//获取前一个汇总日期
		$lastHzDate=$this->_dao->getKC_EveryWeekSettingForLastDate($edata1);
		$edata2=$lastHzDate[0]['EndDate'];
		$sdata2=date("Y-m-d",strtotime($lastHzDate[1]['EndDate']."+1 day"));
		$sdata1=date("Y-m-d",strtotime($edata2."+1 day"));

		//获取上一个月的同期汇总日期
		$LastMDate=date("Y-m-d",strtotime($edata1."-1 month"));
	//	print_r($LastMDate);
		$LastMHzDate1 = $this->_dao->getKC_EveryWeekSettingForLastMDate($LastMDate);
		$LastMHzDate2 = $this->_dao->getKC_EveryWeekSettingForNow($LastMDate);
		if(strtotime($LastMDate)-strtotime($LastMHzDate1[0]['EndDate']) > strtotime($LastMHzDate2['EndDate'])-strtotime($LastMDate)){
			$edata3=$LastMHzDate2['EndDate'];
			$tmpDate=$LastMHzDate1[0]['EndDate'];
		}else{
			$edata3=$LastMHzDate1[0]['EndDate'];
			$tmpDate=$LastMHzDate1[1]['EndDate'];
		}
		$sdata3=date("Y-m-d",strtotime($tmpDate."+1 day"));


		//获取上一年的同期汇总日期
		$LastYDate=date("Y-m-d",strtotime($edata1."-1 year"));
		$lastHzDate1=$this->_dao->getKC_EveryWeekSettingForLastMDate($LastYDate);
		$lastHzDate2 = $this->_dao->getKC_EveryWeekSettingForNow($LastYDate);
		if(strtotime($LastYDate)-strtotime($lastHzDate1[0]['EndDate']) > strtotime($lastHzDate2['EndDate'])-strtotime($LastYDate)){
			$edata4=$lastHzDate2['EndDate'];
            $tmpDate=$lastHzDate1[0]['EndDate'];
        }else{
			$edata4=$lastHzDate1[0]['EndDate'];
			$tmpDate=$lastHzDate1[1]['EndDate'];
		}
		$sdata4=date("Y-m-d",strtotime($tmpDate."+1 day"));
        
		$arr=array("sdata1"=>$sdata1,"edata1"=>$edata1,"sdata2"=>$sdata2,"edata2"=>$edata2,"sdata3"=>$sdata3,"edata3"=>$edata3,"sdata4"=>$sdata4,"edata4"=>$edata4);
		return $arr;
	}
	
}
?>